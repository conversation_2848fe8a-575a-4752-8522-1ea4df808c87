import sys
from PySide6.QtWidgets import (Q<PERSON><PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout,
                               QGridLayout, QLabel, QPushButton, QFrame, QDialog,
                               QLineEdit, QMessageBox, QScrollArea, QSizePolicy, QMenu)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QPixmap, QPainter, QColor, QFont, QPen, QBrush

from src.core.hand_specifications import HandType
from src.gui.components.finger_control_widget import CustomButton
from src.gui.widgets.gesture_edit_dialog import GestureEditDialog
from src.utils.resource_helper import get_image_path
from src.core.gesture_data_manager import GestureDataManager, get_gesture_manager, GestureData


class GestureCard(QFrame):
    clicked = Signal(str)
    edit_requested = Signal(str)

    delete_requested = Signal(str)

    def __init__(self, name, image_path=None, is_custom=False, gesture_data=None, parent=None):
        super().__init__(parent)
        self.name = name
        self.image_path = image_path
        self.is_custom = is_custom
        self.selected = False
        self.gesture_data = gesture_data  # 存储手势数据，包含steps等信息
        self._setup_ui()
        self._setup_style()

        # 设置右键菜单策略
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_context_menu)

    def _show_context_menu(self, position):
        # 只有自定义手势才显示右键菜单
        if not self.is_custom:
            return

        context_menu = QMenu(self)

        # 设置菜单样式
        context_menu.setStyleSheet("""
                QMenu {
                    background-color: #3F4349;
                    border: 1px solid #FF9429;
                    border-radius: 4px;
                    padding: 4px;
                    width: 110px;
                }
                QMenu::item {
                    background-color: transparent;
                    color: white;
                    padding: 6px 12px;
                    border-radius: 2px;
                    width: 100px;
                    height: 30px;
                }
                QMenu::item:selected {
                    background-color: #FF7700;
                }
            """)

        delete_action = context_menu.addAction(self.tr("Delete"))
        delete_action.triggered.connect(self._on_delete_requested)
        edit_action = context_menu.addAction(self.tr("Edit"))
        edit_action.triggered.connect(lambda: self.edit_requested.emit(self.name))

        # 在鼠标位置显示菜单
        context_menu.exec(self.mapToGlobal(position))

    def _on_delete_requested(self):
        self.delete_requested.emit(self.name)
        
    def _setup_ui(self):
        self.setFixedSize(195, 220)
        self.setObjectName("GestureCard")
        
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        self.image_label = QLabel()
        self.image_label.setScaledContents(True)

        if self.image_path:
            self._load_image()
            
        layout.addWidget(self.image_label)
        
        # 名称标签
        self.name_label = QLabel(self.name)
        self.name_label.setAlignment(Qt.AlignCenter)
        self.name_label.setWordWrap(True)
        self.name_label.setMaximumHeight(40)
        self.name_label.setStyleSheet("QLabel {background-color: #22262E; font-size: 20px; color: white;}")

        name_layout = QHBoxLayout()
        name_layout.setContentsMargins(0, 0, 0, 0)
        name_layout.addWidget(self.name_label)

        layout.addLayout(name_layout)
        
        # 如果是自定义手势，添加编辑按钮
        if self.is_custom:
            edit_btn = QPushButton()
            edit_btn.setIcon(QPixmap(get_image_path("edit.svg")))
            edit_btn.setFixedSize(20, 20)
            edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    border: none;
                    font-size: 12px;
                }
            """)
            edit_btn.clicked.connect(lambda: self.edit_requested.emit(self.name))
            name_layout.addWidget(edit_btn)
        
        self.setLayout(layout)

    def update_name(self, name):
        self.name = name
        self.name_label.setText(name)

    def get_gesture_steps(self):
        if self.gesture_data and 'steps' in self.gesture_data:
            return self.gesture_data['steps']
        return []

    def set_gesture_data(self, gesture_data):
        self.gesture_data = gesture_data

    def get_gesture_data(self):
        return self.gesture_data
        
    def _load_image(self):
        pixmap = QPixmap(self.image_path)
        if not pixmap.isNull():
            # 缩放图像以适应标签
            scaled_pixmap = pixmap.scaled(
                self.image_label.size(),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            self.image_label.setPixmap(scaled_pixmap)
        
    def _setup_style(self):
        self.setStyleSheet("""
            #GestureCard {
                background-color: #3F4349;
                border-radius: 10px;
                border: none;
            }
            #GestureCard:hover {
                background-color: #3D3D42;
                border: 1px solid #555555;
            }
            QLabel {
                background-color: transparent;
            }
        """)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            # 所有手势卡片都可以通过单击选中
            self.clicked.emit(self.name)
        super().mousePressEvent(event)

    def mouseDoubleClickEvent(self, event):
        if event.button() == Qt.LeftButton:
            # 只有自定义手势才能双击打开编辑界面
            if self.is_custom:
                self.edit_requested.emit(self.name)
        super().mouseDoubleClickEvent(event)
        
    def set_selected(self, selected):
        self.selected = selected
        if selected:
            self.setStyleSheet("""
                #GestureCard {
                    background-color: #3D3D42;
                    border: 2px solid #FF7700;
                    border-radius: 12px;
                }
                QLabel {
                    color: white;
                    font-size: 12px;
                    font-weight: bold;
                }
            """)
        else:
            self._setup_style()


class AddGestureCard(QFrame):
    clicked = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
        self._setup_style()
        
    def _setup_ui(self):
        self.setFixedSize(195, 200)
        self.setObjectName("AddGestureCard")
        
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        self.image_label = QLabel()
        self.image_label.setScaledContents(True)
        self._load_image(get_image_path("add_gesture.png", "gesture"))
        self.image_label.setStyleSheet("""QLabel {background-color: transparent;}""")
        layout.addWidget(self.image_label, 0, Qt.AlignCenter)
        
        self.setLayout(layout)

    def _load_image(self, img_path):
        pixmap = QPixmap(img_path)
        if not pixmap.isNull():
            scaled_pixmap = pixmap.scaled(
                QSize(99, 118),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            self.image_label.setPixmap(scaled_pixmap)
        
    def _setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            #AddGestureCard {
                background-color: #333338;
                border-radius: 12px;
                border: 2px solid transparent;
            }
            #AddGestureCard:hover {
                background-color: #3D3D42;
                border: 2px solid #666666;
            }
            QLabel {
                color: #AAAAAA;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class EditGestureDialog(QDialog):
    def __init__(self, gesture_name="", parent=None):
        super().__init__(parent)
        self.gesture_name = gesture_name
        self._setup_ui()
        
    def _setup_ui(self):
        self.setWindowTitle("Edit Gesture" if self.gesture_name else "Add Gesture")
        self.setFixedSize(300, 150)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # 名称输入
        name_label = QLabel("Gesture Name:")
        layout.addWidget(name_label)
        
        self.name_input = QLineEdit()
        self.name_input.setText(self.gesture_name)
        self.name_input.setPlaceholderText("Enter gesture name...")
        layout.addWidget(self.name_input)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        ok_btn = QPushButton("OK")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setDefault(True)
        button_layout.addWidget(ok_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
        
        # 样式
        self.setStyleSheet("""
            QDialog {
                background-color: #2A2A2A;
            }
            QLabel {
                color: white;
                font-size: 14px;
            }
            QLineEdit {
                background-color: #333333;
                color: white;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border: 2px solid #FF7700;
            }
            QPushButton {
                background-color: #444444;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #555555;
            }
            QPushButton:pressed {
                background-color: #333333;
            }
        """)
        
    def get_gesture_name(self):
        return self.name_input.text().strip()


class GestureCardWidget(QWidget):
    gesture_selected = Signal(str)  # 选择手势时发出信号

    download_signal = Signal(HandType, int, list)  # 下载当前手势时发出信号
    delete_signal = Signal(HandType, int)  # 删除当前手势时发出信号
    run_signal = Signal(HandType, int)  # 运行当前手势时发出信号

    def __init__(self, hand_type, parent=None):
        super().__init__(parent)
        self.hand_type = hand_type
        self.current_selected = None
        self.gesture_cards = {}  # 存储所有手势卡片 {name: card}
        self.custom_gestures = set()  # 存储自定义手势名称
        self.gesture_manager = get_gesture_manager()  # 获取手势数据管理器

        self.gesture_edit_dialog = GestureEditDialog(parent=self)

        self._setup_ui()
        self._setup_style()
        self._add_default_gestures()

    def set_input_field_range(self, min_positions, max_positions, min_speeds, max_speeds, min_currents, max_currents):
        self.gesture_manager.set_step_input_range(min_positions, max_positions, min_speeds, max_speeds, min_currents, max_currents)

    def load_custom_gestures(self):
        # 加载自定义手势
        custom_gestures = self.gesture_manager.get_custom_gestures()
        for gesture_name, gesture_data in custom_gestures.items():
            if gesture_name not in self.gesture_cards:  # 避免重复添加
                self._create_custom_gesture_from_data(gesture_name, gesture_data)

        # 默认选中第一个手势
        if len(custom_gestures):
            first_gesture = custom_gestures[list(custom_gestures.keys())[-1]].name
            self._select_gesture(first_gesture)

    def _on_gesture_edit_requested(self, gesture_name):
        # 获取手势数据
        gesture_data = self.gesture_manager.get_gesture_data(gesture_name)

        # 只有自定义手势才能打开编辑对话框
        if not gesture_data or not gesture_data.is_custom:
            return

        all_gesture_names = list(self.gesture_manager.get_all_gestures().keys())

        # 更新对话框的数据
        self.gesture_edit_dialog.update_gesture_data(gesture_name, gesture_data, all_gesture_names)
        self.gesture_edit_dialog.set_input_range(self.gesture_manager.step_input_range)

        # 加载手势数据并显示对话框
        self.gesture_edit_dialog.load_gesture_data()
        if self.gesture_edit_dialog.exec() == QDialog.Accepted:
            # 如果用户保存了更改，更新手势数据
            updated_data = self.gesture_edit_dialog.get_gesture_data()
            if updated_data:
                # 保存到数据管理器，传递旧名称以便正确处理名称变更
                self.gesture_manager.save_gesture_data(updated_data, gesture_name)

                # 更新卡片数据
                print(gesture_name, updated_data.name, self.gesture_cards.keys())
                self.gesture_cards[gesture_name].set_gesture_data(updated_data)

                # 如果手势名称发生变化，需要更新字典键，同时保持原有顺序
                if gesture_name != updated_data.name:
                    # 更新卡片名称
                    self.gesture_cards[gesture_name].update_name(updated_data.name)

                    # 更新字典键，保持原有顺序
                    self.gesture_cards[updated_data.name] = self.gesture_cards.pop(gesture_name)

                    # 如果当前选中的是这个手势，更新选中状态
                    if self.current_selected == gesture_name:
                        self.current_selected = updated_data.name

                    # 如果是自定义手势，更新自定义手势集合
                    if gesture_name in self.custom_gestures:
                        self.custom_gestures.remove(gesture_name)
                        self.custom_gestures.add(updated_data.name)

    def _on_gesture_delete_requested(self, gesture_name):
        if gesture_name not in self.custom_gestures:
            return

        delete_action_id = self.gesture_manager.get_gesture_data(gesture_name).action_id

        # 从数据管理器中删除手势
        if self.gesture_manager.delete_gesture(gesture_name):
            # 从UI中移除手势卡片
            if gesture_name in self.gesture_cards:
                gesture_card = self.gesture_cards[gesture_name]
                self.custom_layout.removeWidget(gesture_card)
                gesture_card.deleteLater()
                del self.gesture_cards[gesture_name]

            # 从自定义手势集合中移除
            self.custom_gestures.discard(gesture_name)

            # 如果删除的是当前选中的手势，清除选中状态
            if self.current_selected == gesture_name:
                self.current_selected = None

            # 重新排列剩余的自定义手势卡片
            self._rearrange_custom_cards()

            # 下发清空手势信号给到设备
            self.delete_signal.emit(self.hand_type, delete_action_id)
        else:
            QMessageBox.warning(self, "Error", f"Failed to delete gesture '{gesture_name}'.")

    def _setup_ui(self):
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        title_layout = QHBoxLayout()
        title_label = QLabel("Default Gesture")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        main_layout.addLayout(title_layout)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #333333;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: red;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #888888;
            }
        """)

        # 手势网格容器
        self.gesture_container = QWidget()
        self.gesture_layout = QGridLayout()
        self.gesture_layout.setSpacing(15)
        self.gesture_layout.setContentsMargins(0, 0, 0, 0)
        self.gesture_container.setLayout(self.gesture_layout)

        scroll_area.setWidget(self.gesture_container)
        main_layout.addWidget(scroll_area)

        # 自定义手势标题
        custom_title_layout = QHBoxLayout()
        custom_title_label = QLabel("Customized Gesture")
        custom_title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        custom_title_layout.addWidget(custom_title_label)
        custom_title_layout.addStretch()
        main_layout.addLayout(custom_title_layout)

        # 自定义手势滚动区域
        custom_scroll_area = QScrollArea()
        custom_scroll_area.setWidgetResizable(True)
        custom_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        custom_scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        custom_scroll_area.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Expanding)
        custom_scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: 1px;
            }
            QScrollBar:vertical {
                background-color: #FF9429;
                width: 9px;
                border-radius: 6px;
                border: none;
            }
            QScrollBar::add-line:vertical,
            QScrollBar::sub-line:vertical {
                background: none;
                height: 0px;
            }
            QScrollBar::add-page:vertical,
            QScrollBar::sub-page:vertical {
                background: white;
            }
            QScrollBar::handle:vertical {
                background-color: #FF9429;
                border-radius: 6px;
                min-height: 20px;
            }
        """)

        # 自定义手势网格容器
        self.custom_container = QWidget()
        self.custom_layout = QGridLayout()
        self.custom_layout.setSpacing(15)
        self.custom_layout.setContentsMargins(0, 0, 0, 0)
        self.custom_container.setLayout(self.custom_layout)

        custom_scroll_area.setWidget(self.custom_container)
        main_layout.addWidget(custom_scroll_area)
        main_layout.setStretchFactor(custom_scroll_area, 1)

        # Download按钮和Run
        self.button_download = CustomButton(text=self.tr("Download"), back_color="#FF9429", color="black")
        self.button_run = CustomButton(text=self.tr("Run"), back_color="#FF9429", color="black")
        h_layout = QHBoxLayout()
        h_layout.addWidget(self.button_download)
        h_layout.addStretch()
        h_layout.addWidget(self.button_run)
        main_layout.addLayout(h_layout)
        h_layout.setContentsMargins(20, 0, 20, 0)

        self.button_download.clicked.connect(self._on_download_button_clicked)
        self.button_run.clicked.connect(self._on_run_button_clicked)

        self.add_card = AddGestureCard()
        self.add_card.clicked.connect(self._add_custom_gesture)

        self.setLayout(main_layout)

    def _on_download_button_clicked(self):
        if self.current_selected and self.current_selected in self.gesture_cards:

            gesture_data = self.gesture_manager.get_gesture_data(self.current_selected)
            action_sequence_list = gesture_data.steps_to_list()

            self.download_signal.emit(self.hand_type, gesture_data.action_id, action_sequence_list)

    def _on_run_button_clicked(self):
        if self.current_selected and self.current_selected in self.gesture_cards:
            gesture_data = self.gesture_manager.get_gesture_data(self.current_selected)
            self.run_signal.emit(self.hand_type, gesture_data.action_id)

    def _setup_style(self):
        self.setStyleSheet("""
            QWidget {
                background-color: #22262E;
            }
        """)

    def _add_default_gestures(self):
        default_gestures = [
            ["Open", "open.png"],
            ["Fist", "fist.png"],
            ["Precision Pinch", "precision_pinch.png"],
            ["Tripod Pinch", "tripod_pinch.png"],
            ["Lateral Pinch", "lateral_pinch.png"],
            ["Index Point", "index_point.png"],
        ]

        # 按网格布局添加手势卡片
        max_cols = 6
        for i, (gesture_name, image_name) in enumerate(default_gestures):
            row = i // max_cols
            col = i % max_cols

            image_path = get_image_path(image_name, "gesture")
            gesture_data = self.gesture_manager.get_gesture_data(gesture_name)
            gesture_card = GestureCard(gesture_name, image_path=image_path, is_custom=False, gesture_data=gesture_data)
            gesture_card.clicked.connect(self._on_gesture_clicked)

            self.gesture_cards[gesture_name] = gesture_card
            self.gesture_layout.addWidget(gesture_card, row, col)

        # 初始化时将添加按钮放在第一个位置 (0, 0)
        self.custom_layout.addWidget(self.add_card, 0, 0)

    def _on_gesture_clicked(self, gesture_name):
        self._select_gesture(gesture_name)
        # 不再自动发出gesture_selected信号，避免自动打开编辑对话框

    def _select_gesture(self, gesture_name):
        if self.current_selected and self.current_selected in self.gesture_cards:
            self.gesture_cards[self.current_selected].set_selected(False)

        if gesture_name in list(self.gesture_cards.keys()):
            self.gesture_cards[gesture_name].set_selected(True)
            self.current_selected = gesture_name

    def _add_custom_gesture(self):
        dialog = EditGestureDialog(parent=self)
        if dialog.exec() == QDialog.Accepted:
            gesture_name = dialog.get_gesture_name()
            if gesture_name:
                if gesture_name in self.gesture_cards:
                    QMessageBox.warning(self, "Warning", f"Gesture '{gesture_name}' already exists!")
                    return

                self._create_custom_gesture(gesture_name)

    def _create_custom_gesture(self, gesture_name):
        # 获取下一个可用的action_id
        next_action_id = self.gesture_manager.get_next_available_action_id()

        if next_action_id is None:
            QMessageBox.warning(self, "Warning", "Maximum number of custom gestures (24) reached!")
            return

        # 创建默认的手势数据，使用正确的参数
        default_gesture_data = GestureData(next_action_id, gesture_name, True)

        # 保存到数据管理器
        self.gesture_manager.save_gesture_data(default_gesture_data)

        # 创建手势卡片
        self._create_custom_gesture_from_data(gesture_name, default_gesture_data)

    def _create_custom_gesture_from_data(self, gesture_name, gesture_data):
        gesture_card = GestureCard(gesture_name, is_custom=True, gesture_data=gesture_data)
        gesture_card.clicked.connect(self._on_gesture_clicked)
        gesture_card.edit_requested.connect(self._on_gesture_edit_requested)
        gesture_card.delete_requested.connect(self._on_gesture_delete_requested)

        self.gesture_cards[gesture_name] = gesture_card
        self.custom_gestures.add(gesture_name)

        # 需要先重新排列所有现有的自定义手势卡片（排除刚添加的新卡片）
        self._rearrange_custom_cards(exclude_gesture=gesture_name)

        # 将新卡片添加到位置 (0, 1)
        self.custom_layout.addWidget(gesture_card, 0, 1)

        # 选中新卡片
        self.select_gesture(gesture_name)

    def _rearrange_custom_cards(self, exclude_gesture=None):
        max_cols = 6

        # 获取所有现有的自定义手势卡片（排除指定的手势）
        existing_cards = []
        for name in self.custom_gestures:
            if name != exclude_gesture and name in self.gesture_cards:
                card = self.gesture_cards[name]
                self.custom_layout.removeWidget(card)
                existing_cards.append(card)

        existing_cards = sorted(existing_cards, key=lambda card: card.gesture_data.action_id)

        # 重新排列现有卡片
        if exclude_gesture is None:
            # 删除场景：从位置(0,1)开始排列
            start_position = 1
        else:
            # 添加场景：从位置(0,2)开始排列，为新卡片在(0,1)位置腾出空间
            start_position = 2

        for i, card in enumerate(existing_cards):
            # 计算新位置
            position = i + start_position
            row = position // max_cols
            col = position % max_cols

            self.custom_layout.addWidget(card, row, col)

    def get_selected_gesture(self):
        return self.current_selected

    def select_gesture(self, gesture_name):
        if gesture_name in self.gesture_cards:
            self._select_gesture(gesture_name)


class MainWindow(QWidget):

    def __init__(self):
        super().__init__()
        self._setup_ui()

    def _setup_ui(self):
        self.setWindowTitle("Gesture Card Browser Demo")
        self.setGeometry(100, 100, 900, 700)

        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        # 手势浏览器
        self.gesture_browser = GestureCardWidget()
        # self.gesture_browser.gesture_selected.connect(self._on_gesture_selected)
        layout.addWidget(self.gesture_browser)

        # 状态显示
        self.status_label = QLabel("Selected: Open")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #333333;
                color: white;
                font-size: 14px;
                padding: 10px;
                border-top: 1px solid #555555;
            }
        """)
        layout.addWidget(self.status_label)

        self.setLayout(layout)

        # 设置窗口样式
        self.setStyleSheet("""
            QWidget {
                background-color: #22262E;
            }
        """)

    # def _on_gesture_selected(self, gesture_name):
    #     self.status_label.setText(f"Selected: {gesture_name}")
    #     print(f"Gesture selected: {gesture_name}")
    #
    #     dialog = GestureEditDialog(gesture_name)
    #     dialog.exec()


if __name__ == "__main__":
    app = QApplication(sys.argv)

    window = MainWindow()
    window.show()

    sys.exit(app.exec())
